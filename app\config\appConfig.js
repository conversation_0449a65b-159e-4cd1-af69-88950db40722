/**
 * 应用全局配置
 * 统一管理所有配置项，包括API配置和应用配置
 */

// 环境判断
const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';

// 后端服务器地址配置
export const BACKEND_PORT = 8001;
export const BACKEND_HOST = '**************'; // 根据用户反馈更正的IP地址
export const BACKEND_PROTOCOL = 'http';

//const BACKEND_HOST = '**************'; // 根据错误日志中的IP地址更新
//const BACKEND_PORT = 443;
//const BACKEND_PROTOCOL = 'https';


// 检测是否为真机调试环境
const isMobileDebug = () => {
  // 检查当前运行环境
  try {
    // 尝试检测uni-app环境
    if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
      const systemInfo = uni.getSystemInfoSync();
      // 如果是APP环境
      if (systemInfo.platform === 'android' || systemInfo.platform === 'ios') {
        console.log('检测到移动设备环境:', systemInfo.platform);
        return true;
      }
    }
  } catch (e) {
    console.log('环境检测错误:', e);
  }

  // 其他环境
  return false;
};

// API基础URL
// 开发环境使用相对路径，避免CORS问题
// 生产环境使用完整URL
// 真机调试环境使用完整URL
export const API_BASE_URL = (() => {
  if (isMobileDebug()) {
    // 真机调试环境使用完整URL
    console.log('检测到真机调试环境，使用完整API URL');
    return `${BACKEND_PROTOCOL}://${BACKEND_HOST}:${BACKEND_PORT}/api`;
  } else if (isDev) {
    // 普通开发环境使用相对路径
    return '/api';
  } else {
    // 生产环境使用完整URL
    return `${BACKEND_PROTOCOL}://${BACKEND_HOST}:${BACKEND_PORT}/api`;
  }
})();

// 开发服务器代理配置 - 用于vite.config.js
export const DEV_PROXY_CONFIG = {
  '/api': {
    target: `${BACKEND_PROTOCOL}://${BACKEND_HOST}${BACKEND_PORT ? `:${BACKEND_PORT}` : ''}`,
    changeOrigin: true,
    secure: false,
  }
};

// API前缀 - 用于构建完整的API路径
export const API_PREFIX = API_BASE_URL;

// API请求超时时间（毫秒）
export const API_TIMEOUT = 30000; // 30秒

// API请求重试配置
export const API_RETRY_ENABLED = true;
export const API_MAX_RETRIES = 3;
export const API_RETRY_DELAY = 3000; // 增加到 3 秒以避免触发限流

// 各模块API路径
export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    sendCode: '/auth/send-code',
    loginWithCode: '/auth/login-with-code',
    verify: '/auth/verify',
    logout: '/auth/logout',
  },
  chat: {
    send: '/chat',
    history: '/conversation/history',
  },
  voice: {
    settings: '/voice/settings',
  },
  admin: {
    userStats: '/admin/user-stats',
    onlineUsers: '/admin/online-users',
    chatLimitConfig: '/admin/chat-limit-config',
    speechServiceSettings: '/admin/speech-service-settings',
  },
  mood: {
    add: '/mood/create',
    list: '/mood/list',
    stats: '/mood/stats',
  },
  meditation: {
    list: '/meditation/tracks',
    favorite: '/meditation/tracks/:id/favorite',
  },
  community: {
    groups: '/community/groups',
    posts: '/community/groups/:id/posts',
    replies: '/community/posts/:id/replies',
  },
  user: {
    profile: '/user/profile',
    settings: '/user/settings',
  },
  notifications: {
    list: '/notifications',
    unreadCount: '/notifications/unread-count',
    markAsRead: '/notifications/:id/read',
    markAllAsRead: '/notifications/read-all',
    delete: '/notifications/:id',
    deleteAll: '/notifications',
  },
  counseling: {
    sessions: '/counseling/sessions',
    session: '/counseling/sessions/:id',
    report: '/counseling/sessions/:id/report',
    analyze: '/counseling/analyze',
    rating: '/counseling/sessions/:id/rating',
    recordings: '/counseling/recordings',
    recording: '/counseling/recordings/:id',
    process: '/counseling/recordings/:id/process',
    transcription: '/counseling/recordings/:id/transcription',
    sessionRecordings: '/counseling/sessions/:id/recordings',
  },
};

// 录音相关配置
export const APP_SETTINGS = {
  recording: {
    // 采样率
    sampleRate: 44100,
    // 声道数
    numberOfChannels: 1,
    // 比特率
    bitRate: 128000,
    // 最大录音时长（秒）
    maxDuration: 3600,
    // 最大文件大小（字节）
    maxFileSize: 100 * 1024 * 1024, // 100MB
  }
};

// 配置对象
const appConfig = {
  // 应用基本信息
  app: {
    name: 'SoulSync',
    // 动态获取应用版本号
    get version() {
      try {
        // 在App环境中，优先使用plus.runtime.version获取应用版本号
        //if (typeof plus !== 'undefined' && plus.runtime && plus.runtime.version) {
         // console.log('通过plus.runtime.version获取版本号:', plus.runtime.version);
         // return plus.runtime.version;
        //}

        // 如果plus.runtime.version不可用，尝试使用plus.runtime.getProperty
        if (typeof plus !== 'undefined' && plus.runtime && plus.runtime.getProperty) {
          try {
            // 获取当前应用的appid
            const appid = plus.runtime.appid || 'H5F3D3F2F';
            // 同步方式获取应用信息
            const widgetInfo = plus.runtime.getProperty(appid);
            if (widgetInfo && widgetInfo.version) {
              console.log('通过plus.runtime.getProperty获取版本号:', widgetInfo.version);
              return widgetInfo.version;
            }
          } catch (propError) {
            console.log('通过plus.runtime.getProperty获取版本号失败:', propError);
          }
        }

        // 尝试从系统信息中获取版本号
        if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
          const systemInfo = uni.getSystemInfoSync();
          console.log('系统信息:', JSON.stringify(systemInfo));

          // 优先使用appVersion
          if (systemInfo.appVersion) {
            console.log('通过systemInfo.appVersion获取版本号:', systemInfo.appVersion);
            return systemInfo.appVersion;
          }

          // 其次使用version
          if (systemInfo.version) {
            console.log('通过systemInfo.version获取版本号:', systemInfo.version);
            return systemInfo.version;
          }

          // 再次尝试使用app版本号
          if (systemInfo.app && systemInfo.app.version) {
            console.log('通过systemInfo.app.version获取版本号:', systemInfo.app.version);
            return systemInfo.app.version;
          }
        }
      } catch (e) {
        console.error('获取版本号失败:', e);
      }

      // 如果无法获取版本号，则返回默认版本号
      console.log('无法获取版本号，使用默认版本号: 1.0.0');
      return '1.0.0';
    },
    description: '心灵伙伴 - 您的心理健康助手',
    logo: '/static/logo.png',
    supportEmail: '<EMAIL>',
  },

  // 环境配置
  environment: {
    // 应用环境：development, production, testing
    mode: process.env.NODE_ENV || 'development',
    // 是否为开发环境
    isDevelopment: isDev,
    // 是否为生产环境
    isProduction: isProd,
  },

  // 日志设置
  logging: {
    // 日志级别：debug, info, warn, error, none
    level: isProd ? 'info' : 'debug',
    // 是否启用详细日志
    verbose: true,
    // 是否显示调用堆栈
    showCallerInfo: true,
  },

  // 访客功能限制
  guest: {
    // 访客模式下的最大消息数量
    maxMessages: 50,
  },

  // API相关配置
  api: {
    // API 基础URL
    baseUrl: API_BASE_URL,
    // API 前缀
    prefix: API_PREFIX,
    // API 请求超时时间（毫秒）
    timeout: API_TIMEOUT,
    // 是否启用请求重试
    retryEnabled: API_RETRY_ENABLED,
    // 最大重试次数
    maxRetries: API_MAX_RETRIES,
    // 重试延迟（毫秒）
    retryDelay: API_RETRY_DELAY,
    // 各模块API路径
    endpoints: API_ENDPOINTS,
  },

  // 聊天配置
  chat: {
    // 聊天消息的最大长度
    maxMessageLength: 2000,
    // 聊天历史记录的最大条数
    maxHistoryItems: 100,
    // 聊天消息自动保存间隔（毫秒）
    autoSaveInterval: 5000,
    // 聊天消息的最大显示数量
    maxDisplayMessages: 50,
    // 是否启用语音输入
    enableVoiceInput: false,
    // 是否启用表情选择器
    enableEmojiPicker: true,
    // 是否启用文件上传
    enableFileUpload: false,
  },

  // 用户界面配置
  ui: {
    // 主题
    theme: 'light',
    // 主色调
    primaryColor: '#4a6fa5',
    // 次要色调
    secondaryColor: '#6d9886',
    // 强调色
    accentColor: '#f56a6a',
    // 字体大小（像素）
    fontSize: 16,
    // 行高
    lineHeight: 1.5,
    // 边距（像素）
    spacing: 8,
    // 圆角（像素）
    borderRadius: 4,
    // 阴影
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    // 动画持续时间（毫秒）
    animationDuration: 300,
    // 过渡效果
    transition: 'all 0.3s ease',
  },

  // 认证配置
  auth: {
    // 令牌存储键名
    tokenStorageKey: 'access_token',
    // 用户信息存储键名
    userStorageKey: 'user_info',
    // 令牌过期时间（毫秒）
    tokenExpiration: 7 * 24 * 60 * 60 * 1000, // 7天
    // 是否启用自动登录
    enableAutoLogin: true,
    // 是否启用记住我功能
    enableRememberMe: true,
    // 是否启用社交登录
    enableSocialLogin: false,
  },

  // 性能配置
  performance: {
    // 是否启用缓存
    enableCache: true,
    // 缓存过期时间（毫秒）
    cacheExpiration: 24 * 60 * 60 * 1000, // 24小时
    // 是否启用懒加载
    enableLazyLoading: true,
    // 是否启用代码分割
    enableCodeSplitting: true,
    // 是否启用预加载
    enablePreloading: false,
  },
};

export default appConfig;
